"""
多进程数据更新配置文件
"""

from typing import Dict, Any

class UpdateConfig:
    """数据更新配置类"""
    
    def __init__(self):
        # 数据存储目录配置
        self.data_dirs = {
            'default': "/home/<USER>/Studios/600-codes/dataset/trade_data",
            'backup': "/home/<USER>/Studios/700-dataset/stocks",
            'windows': "E:\\600-codes\\009-dataset"
        }
        
        # 多进程配置
        self.multiprocess_config = {
            'max_processes': 8,  # 最大进程数
            'use_cpu_count': True,  # 是否根据CPU核心数自动调整
            'start_method': 'spawn',  # 进程启动方法
            'timeout': 3600,  # 超时时间（秒）
        }
        
        # 日志配置
        self.logging_config = {
            'level': 'INFO',
            'format': '%(asctime)s - %(process)d - %(levelname)s - %(message)s',
            'file': 'update_data.log',
            'console': True
        }
        
        # 更新策略配置
        self.update_strategy = {
            'update_basic_data': True,  # 是否更新基础数据
            'update_stock_data': True,  # 是否更新股票数据
            'update_index_data': True,  # 是否更新指数数据
            'batch_size': None,  # 每批处理的股票数量（None表示自动分配）
        }
        
        # 重试配置
        self.retry_config = {
            'max_retries': 3,  # 最大重试次数
            'retry_delay': 5,  # 重试延迟（秒）
            'retry_failed_only': True,  # 是否只重试失败的股票
        }
    
    def get_data_dir(self, env: str = 'default') -> str:
        """获取数据目录"""
        return self.data_dirs.get(env, self.data_dirs['default'])
    
    def get_process_count(self) -> int:
        """获取进程数量"""
        if self.multiprocess_config['use_cpu_count']:
            import multiprocessing as mp
            return min(mp.cpu_count(), self.multiprocess_config['max_processes'])
        return self.multiprocess_config['max_processes']
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'data_dirs': self.data_dirs,
            'multiprocess_config': self.multiprocess_config,
            'logging_config': self.logging_config,
            'update_strategy': self.update_strategy,
            'retry_config': self.retry_config
        }
