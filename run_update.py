#!/usr/bin/env python3
"""
数据更新运行脚本
提供简单的命令行界面来运行不同的更新模式
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path


def run_command(command: str, description: str):
    """
    运行命令并显示结果
    """
    print(f"\n🚀 {description}")
    print("=" * 60)
    
    try:
        # 切换到 data_fetch 目录
        data_fetch_dir = Path(__file__).parent / "data_fetch"
        
        result = subprocess.run(
            command,
            shell=True,
            cwd=data_fetch_dir,
            capture_output=False,
            text=True
        )
        
        if result.returncode == 0:
            print(f"✅ {description} 完成")
        else:
            print(f"❌ {description} 失败，返回码: {result.returncode}")
            
    except Exception as e:
        print(f"❌ 运行失败: {str(e)}")


def main():
    parser = argparse.ArgumentParser(
        description="数据更新运行脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_update.py --test          # 运行测试模式
  python run_update.py --full          # 运行完整更新
  python run_update.py --performance   # 运行性能对比
  python run_update.py --config        # 显示配置信息
        """
    )
    
    parser.add_argument(
        "--test", 
        action="store_true", 
        help="运行测试模式（只更新少量股票）"
    )
    
    parser.add_argument(
        "--full", 
        action="store_true", 
        help="运行完整数据更新"
    )
    
    parser.add_argument(
        "--performance", 
        action="store_true", 
        help="运行性能对比分析"
    )
    
    parser.add_argument(
        "--config", 
        action="store_true", 
        help="显示当前配置信息"
    )
    
    parser.add_argument(
        "--check", 
        action="store_true", 
        help="检查环境和依赖"
    )
    
    args = parser.parse_args()
    
    # 如果没有指定任何参数，显示帮助
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    print("🔧 多进程数据更新工具")
    print("=" * 60)
    
    # 检查环境
    if args.check:
        print("\n🔍 检查环境和依赖...")
        print("-" * 30)
        
        # 检查Python版本
        python_version = sys.version_info
        print(f"Python 版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        if python_version < (3, 7):
            print("❌ Python 版本过低，需要 3.7+")
            return
        else:
            print("✅ Python 版本符合要求")
        
        # 检查必要的包
        required_packages = ['baostock', 'pandas', 'numpy']
        for package in required_packages:
            try:
                __import__(package)
                print(f"✅ {package} 已安装")
            except ImportError:
                print(f"❌ {package} 未安装")
        
        # 检查数据目录
        from update_data_config import UpdateConfig
        config = UpdateConfig()
        data_dir = Path(config.get_data_dir())
        
        if data_dir.exists():
            print(f"✅ 数据目录存在: {data_dir}")
        else:
            print(f"⚠️  数据目录不存在: {data_dir}")
            print("   将在首次运行时自动创建")
        
        return
    
    # 显示配置信息
    if args.config:
        print("\n⚙️  当前配置信息")
        print("-" * 30)
        
        try:
            from update_data_config import UpdateConfig
            config = UpdateConfig()
            
            print(f"数据目录: {config.get_data_dir()}")
            print(f"进程数量: {config.get_process_count()}")
            print(f"最大进程数: {config.multiprocess_config['max_processes']}")
            print(f"启动方法: {config.multiprocess_config['start_method']}")
            print(f"日志级别: {config.logging_config['level']}")
            print(f"日志文件: {config.logging_config['file']}")
            
        except Exception as e:
            print(f"❌ 读取配置失败: {str(e)}")
        
        return
    
    # 运行性能对比
    if args.performance:
        run_command(
            "python ../performance_comparison.py",
            "性能对比分析"
        )
        return
    
    # 运行测试模式
    if args.test:
        run_command(
            "python ../update_data_test.py",
            "测试模式数据更新"
        )
        return
    
    # 运行完整更新
    if args.full:
        print("\n⚠️  即将开始完整数据更新")
        print("这可能需要较长时间，请确保:")
        print("1. 网络连接稳定")
        print("2. 有足够的磁盘空间")
        print("3. 系统资源充足")
        
        confirm = input("\n是否继续？(y/N): ").strip().lower()
        if confirm in ['y', 'yes']:
            run_command(
                "python update_data.py",
                "完整数据更新"
            )
        else:
            print("❌ 用户取消操作")
        return


if __name__ == "__main__":
    main()
