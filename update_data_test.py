"""
多进程数据更新测试版本
用于测试多进程功能，只处理少量股票
"""

import baostock as bs
import multiprocessing as mp
import logging
import time
from typing import List, Tuple
from stock.stock_data import StockData
from stock.stock_daily import StockDaily
from update_data_config import UpdateConfig


def test_update_for_stock(stock_code: str, data_dir: str):
    """
    测试版本：只更新股票日线数据
    """
    try:
        # 每个进程需要独立登录baostock
        login_result = bs.login()
        if login_result.error_code != '0':
            logging.error(f"进程中baostock登录失败: {login_result.error_msg}")
            return False, stock_code, f"登录失败: {login_result.error_msg}"
        
        # 只更新日线数据进行测试
        StockDaily(data_dir=data_dir, stock_code=stock_code).update()
        
        # 登出baostock
        bs.logout()
        
        return True, stock_code, "更新成功"
        
    except Exception as e:
        # 确保在异常情况下也要登出
        try:
            bs.logout()
        except:
            pass
        error_msg = f"更新失败: {str(e)}"
        logging.error(f"{stock_code} {error_msg}")
        return False, stock_code, error_msg


def test_update_stocks_batch(stock_codes: List[str], data_dir: str, process_id: int) -> List[Tuple[bool, str, str]]:
    """
    测试版本：批量更新股票数据（单个进程处理）
    """
    results = []
    total_stocks = len(stock_codes)
    
    logging.info(f"测试进程 {process_id} 开始处理 {total_stocks} 只股票")
    
    for i, stock_code in enumerate(stock_codes, 1):
        start_time = time.time()
        success, code, message = test_update_for_stock(stock_code, data_dir)
        elapsed_time = time.time() - start_time
        
        results.append((success, code, message))
        
        if success:
            logging.info(f"测试进程 {process_id} [{i}/{total_stocks}] {stock_code} 更新成功 (耗时: {elapsed_time:.2f}s)")
        else:
            logging.error(f"测试进程 {process_id} [{i}/{total_stocks}] {stock_code} 更新失败: {message}")
    
    logging.info(f"测试进程 {process_id} 完成，成功: {sum(1 for r in results if r[0])}/{total_stocks}")
    return results


def split_list(lst: List, n: int) -> List[List]:
    """
    将列表分割成n个子列表
    """
    k, m = divmod(len(lst), n)
    return [lst[i*k+min(i, m):(i+1)*k+min(i+1, m)] for i in range(n)]


def test_main():
    """
    测试主函数：多进程更新少量股票数据
    """
    config = UpdateConfig()
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format=config.logging_config['format'],
        handlers=[
            logging.FileHandler('update_data_test.log'),
            logging.StreamHandler()
        ]
    )
    
    # 设置数据存储目录
    data_dir = config.get_data_dir()
    StockData().setup(data_dir=data_dir)
    
    logging.info("开始多进程数据更新测试...")
    
    # 主进程登录baostock进行基础数据更新
    login_result = bs.login()
    if login_result.error_code != '0':
        logging.error(f"主进程baostock登录失败: {login_result.error_msg}")
        return
    
    try:
        # 只更新股票基本信息用于测试
        logging.info("更新股票基本信息...")
        StockData().stock_basic.update()
        logging.info("基础数据更新完成")
        
    finally:
        bs.logout()
    
    # 获取股票列表（只取前20只进行测试）
    all_stocks = StockData().stock_basic.stock_codes()
    test_stocks = all_stocks[:20]  # 只测试前20只股票
    total_stocks = len(test_stocks)
    logging.info(f"测试模式：共需要更新 {total_stocks} 只股票的数据")
    
    # 确定进程数量
    num_processes = min(config.get_process_count(), 4)  # 测试时最多用4个进程
    logging.info(f"使用 {num_processes} 个进程进行并行更新测试")
    
    # 分割股票列表
    stock_batches = split_list(test_stocks, num_processes)
    
    # 记录开始时间
    start_time = time.time()
    
    # 使用进程池进行并行处理
    with mp.Pool(processes=num_processes) as pool:
        # 准备参数
        args = [(batch, data_dir, i) for i, batch in enumerate(stock_batches)]
        
        # 并行执行股票数据更新
        logging.info("开始并行更新股票数据测试...")
        results = pool.starmap(test_update_stocks_batch, args)
    
    # 统计结果
    total_success = 0
    total_failed = 0
    failed_stocks = []
    
    for batch_results in results:
        for success, stock_code, message in batch_results:
            if success:
                total_success += 1
            else:
                total_failed += 1
                failed_stocks.append((stock_code, message))
    
    # 计算总耗时
    total_time = time.time() - start_time
    
    # 输出统计信息
    logging.info("=" * 60)
    logging.info("多进程数据更新测试完成！")
    logging.info(f"总耗时: {total_time:.2f} 秒")
    logging.info(f"股票总数: {total_stocks}")
    logging.info(f"更新成功: {total_success}")
    logging.info(f"更新失败: {total_failed}")
    logging.info(f"成功率: {total_success/total_stocks*100:.1f}%")
    
    if failed_stocks:
        logging.warning("失败的股票:")
        for stock_code, error_msg in failed_stocks:
            logging.warning(f"  {stock_code}: {error_msg}")
    
    logging.info("=" * 60)
    
    # 性能对比信息
    estimated_serial_time = total_time * num_processes
    speedup = estimated_serial_time / total_time if total_time > 0 else 0
    logging.info(f"预估串行耗时: {estimated_serial_time:.2f} 秒")
    logging.info(f"多进程加速比: {speedup:.2f}x")


if __name__ == "__main__":
    # 设置多进程启动方法
    mp.set_start_method('spawn', force=True)
    test_main()
