import baostock as bs
import tushare as ts
import colorama
import logging

from tushare.pro.client import DataApi
from ratelimiter import RateLimiter

# max_calls 最多可执行数；period 一个时间控制周期（秒）
# 即一秒内执行一次
ts_rate_limiter = RateLimiter(max_calls=1, period=5)


def bao_login():
    """
    登录baostock接口
    """
    login_result = bs.login()
    if login_result.error_code == "0":
        # 登录成功，使用绿色输出成功信息
        logging.info(colorama.Fore.GREEN + "baostock login %s" % login_result.error_msg)
    else:
        # 登录失败，使用红色输出错误信息
        logging.info(colorama.Fore.RED + "baostock login %s" % login_result.error_msg)
        return Exception(login_result.error_msg)
    return True


def bao_logout():
    """
    登出baostock接口
    """
    bs.logout()
    # 登出成功，使用绿色输出成功信息
    logging.info(colorama.Fore.GREEN + "baostock logout success")


def ts_pro_api() -> DataApi:
    return ts.pro_api(ts_token())


def ts_token() -> str:
    return "a75d665926722b3403a307f205eb296f1dfce8d102bc7f812dbe52f1"


def ts_code(code: str) -> str:
    """
    转换证券代码格式

    参数:
    code (str): 输入的证券代码

    返回:
    str: 转换后的证券代码

    异常:
    Exception: 当输入的证券代码格式无效时抛出异常
    """
    if len(code) != 9:
        raise Exception("无效的证券代码： 长度不符")
    stock_code = code.upper()
    if stock_code.endswith(".SZ") or stock_code.endswith(".SH"):
        return stock_code
    elif stock_code.startswith("SZ.") or stock_code.startswith("SH."):
        return "%s.%s" % (stock_code[3:], stock_code[0:2])
    else:
        raise Exception("无效的证券代码： %s" % code)
