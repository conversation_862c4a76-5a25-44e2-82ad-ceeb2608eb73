import logging
import os
import colorama
import pandas as pd
import baostock as bs

from datetime import date
from typing import Union, List
from tools.helper import need_update
from tools.data_provider import ts_rate_limiter, ts_pro_api


class StockBasic(object):

    def __init__(self, data_dir: str):
        self.data_dir: str = data_dir
        self.dataframe: Union[pd.DataFrame, None] = None

    def file_path(self) -> str:
        """
        返回保存交易日历的csv文件路径
        :return:
        """
        return os.path.join(self.data_dir, "security_basic", "stock_basic.csv")

    def _setup_dir_(self):
        """
        初始化数据目录
        :return:
        """
        os.makedirs(os.path.dirname(self.file_path()), exist_ok=True)

    def update(self):
        self._setup_dir_()  # 新建stock_basic目录
        self.prepare()  # self.dataframe需要初始化
        if self.should_update():  # 在这里判断是否需要更新数据
            # df = self.ts_stock_basic()
            df = self.bs_stock_basic()
            df.to_csv(path_or_buf=self.file_path(), index=False)

    def should_update(self) -> bool:
        """
        判断数据是否需要更新.(更新频率: 每周更新)
        :return:
        """
        return need_update(self.file_path(), 7)

    def load(self) -> pd.DataFrame:
        # 实例化时加载已有数据
        # 如果stock_basic/stock_basic.csv存在，则加载已有的数据到self.dataframe
        # 如果是baostock，则code读入为字符串，ipoDate和outDate读入为datetime
        # 如果是tushare，则symbol读入为字符串，list_date和delis_date读入为datetime
        if os.path.exists(self.file_path()):
            self.dataframe = pd.read_csv(
                filepath_or_buffer=self.file_path(),
                dtype={"code": str},
                parse_dates=["ipoDate", "outDate"],
                # dtype={"symbol", str},
                # parse_dates=["list_date", "delis_date"],
            )
            self.dataframe.set_index(keys="code", drop=False, inplace=True)
            # self.dataframe.set_index(keys="ts_code", drop=False, inplace=True)
            self.dataframe.sort_index(inplace=True)
        else:
            logging.warning(
                colorama.Fore.RED
                + "[股票列表基础信息] 本地数据文件不存在,请及时下载更新"
            )
            self.dataframe = pd.DataFrame()

        return self.dataframe

    def prepare(self):
        if self.dataframe is None:
            self.load()
        return self

    def list_date_of(self, code) -> date:
        # 确保代码格式转换正确
        if code.startswith("sh.") or code.startswith("sz."):
            bao_code = code
        else:
            # 如果是 600000.SH 格式,需要转换为 sh.600000
            market = "sh." if code.endswith(".SH") else "sz."
            stock_code = code.split(".")[0]
            bao_code = f"{market}{stock_code}"

        try:
            ipo_date = self.dataframe.loc[bao_code].loc["ipoDate"]
            return ipo_date.date()
        except KeyError:
            logging.warning(f"找不到股票 {code} 的上市日期信息")
            return None

    # def name_of(self, code: str) -> str:
    #     """
    #     返回指定证券的名称
    #     :param ts_code:
    #     :return:
    #     """
    #     self.prepare()
    #     return self.dataframe.loc[code].loc["code_name"]

    @staticmethod
    # stock_basic 接口需要2000积分
    @ts_rate_limiter
    def ts_stock_basic() -> pd.DataFrame:
        df: pd.DataFrame = ts_pro_api().stock_basic(
            exchange="",
            list_status="L",
            fields="ts_code,symbol,name,area,industry,fullname,enname,cnspell,market,exchange,curr_type,list_status,list_date,delist_date,is_hs,act_name,act_ent_type",
        )
        df["list_date"] = pd.to_datetime(df["list_date"], format="%Y%m%d")
        df["delist_date"] = pd.to_datetime(df["delist_date"], format="%Y%m%d")
        logging.info(colorama.Fore.YELLOW + "下载股票列表基础信息数据（每周更新）")
        return df

    @staticmethod
    def bs_stock_basic() -> pd.DataFrame:
        # 获取所有状态的股票
        result = bs.query_stock_basic()
        if result.error_code != "0":
            logging.error(f"查询股票基本信息失败: {result.error_msg}")
            return pd.DataFrame()

        df: pd.DataFrame = result.get_data()

        if df.empty:
            logging.warning("获取到的股票基本信息为空")
            return df

        df = df[df["type"] == "1"]

        # 转换日期格式
        for date_col in ["ipoDate", "outDate"]:
            df[date_col] = pd.to_datetime(
                df[date_col], format="%Y-%m-%d", errors="coerce"
            )

        logging.info(
            colorama.Fore.YELLOW
            + f"从 baostock 下载 [股票基本信息] 数据成功，共 {len(df)} 条记录"
        )
        return df

    def stock_codes(self) -> List[str]:
        """
        返回所有股票代码列表
        :return: 股票代码列表
        """
        self.prepare()
        if self.dataframe.empty:
            return []
        return self.dataframe["code"].tolist()
