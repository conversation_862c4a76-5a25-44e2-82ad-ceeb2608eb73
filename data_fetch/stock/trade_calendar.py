import os
import pandas as pd
import baostock as bs
import logging
import colorama
from datetime import date, datetime
from typing import Union, List, Iterable
from tools.singleton import Singleton


# from pandas import Timestamp
# from datetime import date, datetime
# from typing import List, Union, Iterable
# from tools.data_provider import ts_rate_limiter, ts_pro_api

# from data_provider import ts_pro_api


class TradeCalendar(object):

    def __init__(self, data_dir: str):
        """
        沪深A股交易日历对象, 用于获取和更新本地数据, 提供相关查询接口
        :param data_dir:
        """
        self.data_dir = data_dir
        self.dataframe: Union[pd.DataFrame, None] = None

    def file_path(self) -> str:
        """
        返回保存交易日历的csv文件路径
        :return:
        """
        return os.path.join(self.data_dir, "security_basic", "trade_calendar.csv")

    def _setup_dir_(self):
        """
        初始化数据目录
        :return:
        """
        os.makedirs(os.path.dirname(self.file_path()), exist_ok=True)

    def load(self) -> pd.DataFrame:
        """加载交易日历数据"""
        if os.path.exists(self.file_path()):
            self.dataframe = pd.read_csv(
                filepath_or_buffer=self.file_path(),
                parse_dates=["calendar_date"],
                dtype={"is_trading_day": bool},
                # parse_dates=["cal_date", "pretrade_date"],
                # dtype={"is_open": np.bool_},
            )
            # self.dataframe.set_index(keys="cal_date", drop=False, inplace=True)
            self.dataframe.set_index(keys="calendar_date", drop=False, inplace=True)
            self.dataframe.sort_index(inplace=True)
        else:
            self.dataframe = pd.DataFrame(columns=["calendar_date", "is_trading_day"])

        return self.dataframe

    def prepare(self):
        if self.dataframe is None:
            self.load()
        return self

    def update(self):
        self._setup_dir_()
        self.load()
        df_list: List[pd.DataFrame] = [self.dataframe]

        for year in range(1990, pd.Timestamp.today().year + 1):
            check_date = "%s-01-01" % year
            df_tmp: pd.DataFrame = self.dataframe.loc[check_date:check_date]
            if df_tmp.empty:
                new_df = self.bs_trade_cal(
                    start_date="%s-01-01" % year, end_date="%s-12-31" % year
                )
                if not new_df.empty:
                    df_list.append(new_df)

        non_empty_dfs = [df for df in df_list if not df.empty]
        if len(non_empty_dfs) > 1:
            self.dataframe = (
                pd.concat(non_empty_dfs)
                .drop_duplicates(subset="calendar_date")
                .sort_index()
            )
            self.dataframe.to_csv(path_or_buf=self.file_path(), index=False)

    # def load(self) -> pd.DataFrame:
    #     """
    #     从数据文件加载交易日历
    #     :return:
    #     """
    #     # 如果交易日历的csv文件存在，则直接读csv文件
    #     if os.path.exists(self.file_path()):
    #         self.dataframe = pd.read_csv(
    #             filepath_or_buffer=self.file_path(),
    #             dtype={"is_open": np.bool_},
    #             parse_dates=["cal_date", "pretrade_date"],
    #         )
    #         self.dataframe.set_index(keys="cal_date", drop=False, inplace=True)
    #         self.dataframe.sort_index(inplace=True)
    #     else:
    #         self.dataframe = pd.DataFrame(
    #             columns=["cal_date", "is_open", "pretrade_date"]
    #         )

    #     return self.dataframe

    def latest_trade_day(self) -> date:
        """
        返回距离当天之前最近的一个交易日的日期。如果当天是交易日，则返回当天
        :return:
        """
        self.prepare()
        today = date.today()

        # 如果数据为空，尝试更新
        if self.dataframe.empty:
            self.update()
            if self.dataframe.empty:
                raise ValueError("无法获取交易日历数据")

        # 确保索引是日期类型
        if not isinstance(self.dataframe.index, pd.DatetimeIndex):
            self.dataframe.index = pd.to_datetime(self.dataframe.index)

        # 从今天开始向前查找最近的交易日
        while today >= self.dataframe.index.min().date():
            try:
                row = self.dataframe.loc[today]
                if row.loc["is_trading_day"]:
                    return today
            except KeyError:
                pass
            # 如果当前日期不在数据，尝试前一天
            today -= pd.Timedelta(days=1)

        # 如果没有找到交易日，返回数据中的最后一个交易日
        return self.dataframe[self.dataframe["is_trading_day"]].index.max().date()

    def next_n_trade_day(
        self, base_date: date, n: int, last_date: Union[None, date] = None
    ) -> date:
        """
        返回 base_date 后第n个交易日的日期.
        :param last_date:
        :param base_date:
        :param n:
        :return:
        """
        self.prepare()
        df: pd.DataFrame = self.dataframe
        df = df[
            (df["calendar_date"] >= str(base_date)) & (df["is_trading_day"] == True)
        ]
        rows_count = df.shape[0]
        row_index = min(rows_count - 1, n)
        day = df.iloc[row_index].loc["calendar_date"].date()
        if last_date is None:
            return day
        else:
            latest_day = self.latest_trade_day()
            return min(day, latest_day)

    def trade_day_between(self, from_date: date, to_date: date) -> Iterable[date]:
        """
        返回指定起始日期之间(包含起始日期)所有交易日的日期
        :param from_date:
        :param to_date:
        :return:
        """
        self.prepare()
        df: pd.DataFrame = self.dataframe
        df = df[
            (df["cal_date"] >= str(from_date))
            & (df["cal_date"] <= str(to_date))
            & (df["is_open"] == True)
        ]
        for index, value in df["cal_date"].iteritems():
            yield value.date()

    # @staticmethod
    # def end_date() -> str:
    #     today = date.today()
    #     return "%s1231" % today.year

    @staticmethod
    def bs_trade_cal(start_date: str, end_date: str) -> pd.DataFrame:
        """
        使用baostock获取交易日历数据
        :param start_date:
        :param end_date:
        :return:
        """
        rs = bs.query_trade_dates(start_date=start_date, end_date=end_date)
        data_list = []
        while (rs.error_code == "0") & rs.next():
            data_list.append(rs.get_row_data())
        df = pd.DataFrame(data_list, columns=rs.fields)
        df["calendar_date"] = pd.to_datetime(df["calendar_date"])
        df["is_trading_day"] = df["is_trading_day"] == "1"
        df.set_index(keys="calendar_date", drop=False, inplace=True)
        df.sort_index(inplace=True)
        logging.info(
            colorama.Fore.YELLOW + "下载交易日历数据: %s -- %s" % (start_date, end_date)
        )
        return df

    # @staticmethod
    # @ts_rate_limiter
    # def ts_trade_cal(start_date: str, end_date: str) -> pd.DataFrame:
    #     df: pd.DataFrame = ts_pro_api().trade_cal(
    #         exchange="SSE",
    #         start_date=start_date,
    #         end_date=end_date,
    #         fields=",".join(["cal_date", "is_open", "pretrade_date"]),
    #     )
    #     df["cal_date"] = pd.to_datetime(df["cal_date"], format="%Y%m%d")
    #     df["pretrade_date"] = pd.to_datetime(df["pretrade_date"], format="%Y%m%d")
    #     df["is_open"] = df["is_open"].apply(lambda x: str(x) == "1")
    #     df.set_index(keys="cal_date", drop=False, inplace=True)
    #     df.sort_index(inplace=True)
    #     logging.info(
    #         colorama.Fore.YELLOW + "下载交易日历数据: %s -- %s" % (start_date, end_date)
    #     )
    #     return df
