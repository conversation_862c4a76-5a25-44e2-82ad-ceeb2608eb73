import logging
import os
import baostock as bs
import colorama
import numpy as np
import pandas as pd

from datetime import date, timedelta
from typing import Union, List
from .stock_data import StockData

# from data_provider import ts_code
from tools.helper import need_update_by_trade_date


class Stock30Min(object):
    base_date = date(year=2019, month=1, day=2)

    def __init__(self, data_dir: str, stock_code: str):
        self.data_dir = data_dir
        self.stock_code = stock_code
        self.dataframe: Union[pd.DataFrame, None] = None

    def file_path(self) -> str:
        """
        返回保存数据的csv文件路径
        :return:
        """
        return os.path.join(self.data_dir, "stocks", self.stock_code, "30min.csv")

    def _setup_dir_(self):
        """
        初始化数据目录
        :return:
        """
        os.makedirs(os.path.dirname(self.file_path()), exist_ok=True)

    def should_update(self) -> bool:
        """
        如果数据文件的最后修改日期, 早于最近的一个交易日, 则需要更新数据
        如果文件不存在, 直接返回 True
        :return:
        """
        if not os.path.exists(self.file_path()):
            return True

        self.prepare()

        return need_update_by_trade_date(self.dataframe, "date")

    def load(self) -> pd.DataFrame:
        if os.path.exists(self.file_path()):
            self.dataframe = pd.read_csv(
                filepath_or_buffer=self.file_path(),
                parse_dates=["time", "date"],
                dtype={
                    "open": np.float64,
                    "high": np.float64,
                    "low": np.float64,
                    "close": np.float64,
                    "volume": np.float64,
                    "amount": np.float64,
                },
            )
            self.dataframe.set_index(keys="time", drop=False, inplace=True)
            self.dataframe.sort_index(inplace=True)
        else:
            logging.warning(
                colorama.Fore.RED
                + "%s 本地 30min 线数据文件不存在,请及时下载更新" % self.stock_code
            )
            self.dataframe = pd.DataFrame()

        return self.dataframe

    def prepare(self):
        if self.dataframe is None:
            self.load()
        return self

    def start_date(self) -> date:
        """
        计算本次更新的起始日期
        :return:
        """
        self.prepare()

        if self.dataframe.empty:
            return self.base_date
        else:
            return self.dataframe.iloc[-1].loc["date"].date() + timedelta(days=1)

    def update(self):
        if self.should_update():
            list_date = StockData().stock_basic.list_date_of(self.stock_code)
            start_date: date = (
                max(self.start_date(), list_date)
                if list_date is not None
                else self.start_date()
            )
            end_date: date = start_date
            last_trade_day = StockData().trade_calendar.latest_trade_day()
            df_list: List[pd.DataFrame] = []
            if not self.dataframe.empty:
                df_list.append(self.dataframe)
            step_days = timedelta(days=50)

            while start_date <= last_trade_day:
                end_date = min(start_date + step_days, last_trade_day)
                rs = bs.query_history_k_data_plus(
                    code=self.stock_code,
                    fields="date,time,code,open,high,low,close,volume,amount,adjustflag",
                    start_date=str(start_date),
                    end_date=str(end_date),
                    frequency="30",
                    adjustflag="3",
                )
                df_30min = rs.get_data()
                if not df_30min.empty:
                    df_30min["date"] = pd.to_datetime(
                        df_30min["date"], format="%Y-%m-%d"
                    )
                    df_30min["time"] = df_30min["time"].apply(
                        lambda x: pd.to_datetime(x[:-3], format="%Y%m%d%H%M%S")
                    )
                    # df_30min["code"] = df_30min["code"].apply(lambda x: ts_code(x))
                    df_30min["open"] = df_30min["open"].astype(np.float64)
                    df_30min["high"] = df_30min["high"].astype(np.float64)
                    df_30min["low"] = df_30min["low"].astype(np.float64)
                    df_30min["close"] = df_30min["close"].astype(np.float64)
                    # df_30min["volume"] = df_30min["volume"].astype(np.float64)
                    # df_30min["amount"] = df_30min["amount"].astype(np.float64)
                    df_30min.set_index(keys="time", drop=False, inplace=True)
                    logging.debug(
                        colorama.Fore.YELLOW
                        + "下载 %s 30min 线数据, 从 %s 到 %s 共 %s 条"
                        % (self.stock_code, start_date, end_date, df_30min.shape[0])
                    )

                    df_list.append(df_30min)

                start_date = end_date + timedelta(days=1)

            if df_list:  # 只有在获取到数据时才创建文件
                self.dataframe = pd.concat(df_list).drop_duplicates()
                self.dataframe.sort_index(inplace=True)

                # 确保目录存在并保存文件
                self._setup_dir_()
                self.dataframe.to_csv(path_or_buf=self.file_path(), index=False)

                logging.info(
                    colorama.Fore.YELLOW
                    + "%s 30min 线数据更新到: %s path: %s"
                    % (self.stock_code, str(end_date), self.file_path())
                )
            else:
                logging.warning(
                    colorama.Fore.RED + "%s 未获取到30min线数据" % self.stock_code
                )
        else:
            logging.info(
                colorama.Fore.BLUE + "%s 30min 线数据无须更新" % self.stock_code
            )
