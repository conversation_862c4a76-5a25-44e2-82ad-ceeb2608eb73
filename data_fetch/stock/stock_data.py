from typing import Union
from tools.singleton import SingletonMeta

from .trade_calendar import TradeCalendar

from .stock_basic import StockBasic
from .index_basic import IndexBasic
from .etf_basic import ETFBasic
from .cb_basic import CBBasic
from .stock_company import StockCompany
from stock_pools.hs300 import HS300
from stock_pools.zz500 import ZZ500

# from pools.a_all import AALL
# from index_basic import IndexBasic


class StockData(object, metaclass=SingletonMeta):
    def __init__(self):
        # 初始化时需要准备以下数据:
        # - data_dir: 数据存储目录
        # - trade_calendar: 交易日历数据
        # - stock_basic: 股票基本信息数据
        # - stock_company: 上市公司基本信息数据
        # - hs300: 沪深300指数成分股数据
        # - zz500: 中证500指数成分股数据
        # - a_all: 所有A股股票数据
        # - index_basic: 指数基本信息数据
        self._data_dir = ""
        self._trade_calendar: Union[None, TradeCalendar] = None
        self._stock_basic: Union[None, StockBasic] = None
        self._index_basic: Union[None, IndexBasic] = None
        self._etf_basic: Union[None, ETFBasic] = None
        self._cb_basic: Union[None, CBBasic] = None
        self._stock_company: Union[None, StockCompany] = None
        self._hs300: Union[None, HS300] = None
        self._zz500: Union[None, ZZ500] = None
        # self._a_all: Union[None, AALL] = None

    # 设置数据存储的目录
    def setup(self, data_dir: str):
        self._data_dir = data_dir
        return self

    @property
    def data_dir(self) -> str:
        """
        本地数据目录
        :return:
        """
        if self._data_dir is None:
            raise Exception('请先调用StockData().setup(data_dir="指定路径")')

        return self._data_dir

    @property
    def trade_calendar(self) -> TradeCalendar:
        """
        交易日历数据
        :return:
        """
        if self._trade_calendar is None:
            self._trade_calendar = TradeCalendar(self._data_dir)
            self._trade_calendar.load()

        return self._trade_calendar

    @property
    def stock_basic(self) -> StockBasic:
        """
        获取股票基本数据
        """
        if self._stock_basic is None:
            self._stock_basic = StockBasic(self._data_dir)
            self._stock_basic.load()
        return self._stock_basic

    @property
    def index_basic(self) -> IndexBasic:
        """
        获取指数基本数据
        """
        if self._index_basic is None:
            self._index_basic = IndexBasic(self._data_dir)
            self._index_basic.load()
        return self._index_basic

    @property
    def etf_basic(self) -> ETFBasic:
        """
        获取ETF基本数据
        """
        if self._etf_basic is None:
            self._etf_basic = ETFBasic(self._data_dir)
            self._etf_basic.load()
        return self._etf_basic

    @property
    def cb_basic(self) -> CBBasic:
        """
        获取可转债基本数据
        """
        if self._cb_basic is None:
            self._cb_basic = CBBasic(self._data_dir)
            self._cb_basic.load()
        return self._cb_basic

    @property
    def stock_company(self) -> StockCompany:
        """
        获取上市公司基本数据
        """
        if self._stock_company is None:
            self._stock_company = StockCompany(self._data_dir)
            self._stock_company.load()
        return self._stock_company

    @property
    def hs300(self) -> HS300:
        """
        获取沪深300成分股数据
        """
        if self._hs300 is None:
            self._hs300 = HS300(self._data_dir)
            self._hs300.load()
        return self._hs300

    @property
    def zz500(self) -> ZZ500:
        """
        获取中证500成分股数据
        """
        if self._zz500 is None:
            self._zz500 = ZZ500(self._data_dir)
            self._zz500.load()
        return self._zz500

    # @property
    # def a_all(self) -> AALL:
    #     if self._a_all is None:
    #         self._a_all = AALL(self._data_dir)
    #         self._a_all.load()
    #     return self._a_all
