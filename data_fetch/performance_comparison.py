"""
性能对比脚本
比较串行模式和多进程模式的性能差异
"""

import time
import logging
from typing import List
from update_data_config import UpdateConfig


def simulate_serial_update(stock_codes: List[str], avg_time_per_stock: float = 2.0) -> dict:
    """
    模拟串行更新性能
    """
    start_time = time.time()
    total_stocks = len(stock_codes)
    
    logging.info(f"模拟串行更新 {total_stocks} 只股票...")
    
    # 模拟串行处理
    for i, stock_code in enumerate(stock_codes, 1):
        # 模拟处理时间
        time.sleep(0.01)  # 实际中用很短的时间模拟
        if i % 10 == 0:
            logging.info(f"串行模式: 已处理 {i}/{total_stocks} 只股票")
    
    total_time = time.time() - start_time
    estimated_real_time = total_stocks * avg_time_per_stock
    
    return {
        'mode': '串行模式',
        'total_stocks': total_stocks,
        'simulated_time': total_time,
        'estimated_real_time': estimated_real_time,
        'avg_time_per_stock': avg_time_per_stock
    }


def simulate_parallel_update(stock_codes: List[str], num_processes: int, avg_time_per_stock: float = 2.0) -> dict:
    """
    模拟多进程并行更新性能
    """
    start_time = time.time()
    total_stocks = len(stock_codes)
    
    logging.info(f"模拟多进程更新 {total_stocks} 只股票，使用 {num_processes} 个进程...")
    
    # 分割股票列表
    def split_list(lst: List, n: int) -> List[List]:
        k, m = divmod(len(lst), n)
        return [lst[i*k+min(i, m):(i+1)*k+min(i+1, m)] for i in range(n)]
    
    stock_batches = split_list(stock_codes, num_processes)
    max_batch_size = max(len(batch) for batch in stock_batches)
    
    # 模拟并行处理（实际上是串行模拟）
    for i in range(max_batch_size):
        time.sleep(0.01)  # 模拟处理时间
        processed = min((i + 1) * num_processes, total_stocks)
        if (i + 1) % 10 == 0:
            logging.info(f"并行模式: 已处理 {processed}/{total_stocks} 只股票")
    
    total_time = time.time() - start_time
    estimated_real_time = (total_stocks * avg_time_per_stock) / num_processes
    
    return {
        'mode': '多进程模式',
        'total_stocks': total_stocks,
        'num_processes': num_processes,
        'simulated_time': total_time,
        'estimated_real_time': estimated_real_time,
        'avg_time_per_stock': avg_time_per_stock,
        'max_batch_size': max_batch_size
    }


def calculate_performance_metrics(serial_result: dict, parallel_result: dict) -> dict:
    """
    计算性能指标
    """
    speedup = serial_result['estimated_real_time'] / parallel_result['estimated_real_time']
    efficiency = speedup / parallel_result['num_processes']
    time_saved = serial_result['estimated_real_time'] - parallel_result['estimated_real_time']
    time_saved_percentage = (time_saved / serial_result['estimated_real_time']) * 100
    
    return {
        'speedup': speedup,
        'efficiency': efficiency,
        'time_saved_seconds': time_saved,
        'time_saved_percentage': time_saved_percentage,
        'parallel_efficiency': efficiency * 100
    }


def format_time(seconds: float) -> str:
    """
    格式化时间显示
    """
    if seconds < 60:
        return f"{seconds:.1f} 秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f} 分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f} 小时"


def main():
    """
    主函数：性能对比分析
    """
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    config = UpdateConfig()
    
    # 模拟股票列表（实际场景中的股票数量）
    stock_count_scenarios = [50, 100, 500, 1000, 3000, 5000]
    avg_time_per_stock = 3.0  # 假设每只股票平均需要3秒
    
    print("=" * 80)
    print("多进程数据更新性能对比分析")
    print("=" * 80)
    
    for stock_count in stock_count_scenarios:
        print(f"\n📊 场景分析：{stock_count} 只股票")
        print("-" * 50)
        
        # 生成模拟股票代码
        stock_codes = [f"sh.{600000 + i:06d}" for i in range(stock_count)]
        
        # 串行模式性能
        serial_result = simulate_serial_update(stock_codes, avg_time_per_stock)
        
        # 多进程模式性能
        num_processes = config.get_process_count()
        parallel_result = simulate_parallel_update(stock_codes, num_processes, avg_time_per_stock)
        
        # 计算性能指标
        metrics = calculate_performance_metrics(serial_result, parallel_result)
        
        # 输出结果
        print(f"🔄 串行模式:")
        print(f"   预估耗时: {format_time(serial_result['estimated_real_time'])}")
        
        print(f"⚡ 多进程模式 ({num_processes} 进程):")
        print(f"   预估耗时: {format_time(parallel_result['estimated_real_time'])}")
        print(f"   加速比: {metrics['speedup']:.2f}x")
        print(f"   效率: {metrics['parallel_efficiency']:.1f}%")
        print(f"   节省时间: {format_time(metrics['time_saved_seconds'])} ({metrics['time_saved_percentage']:.1f}%)")
        
        # 不同进程数的对比
        print(f"\n📈 不同进程数对比:")
        for processes in [2, 4, 6, 8, 12, 16]:
            if processes <= 16:  # 限制最大进程数
                parallel_test = simulate_parallel_update(stock_codes, processes, avg_time_per_stock)
                test_metrics = calculate_performance_metrics(serial_result, parallel_test)
                print(f"   {processes:2d} 进程: {format_time(parallel_test['estimated_real_time']):>12} "
                      f"(加速比: {test_metrics['speedup']:>5.2f}x, 效率: {test_metrics['parallel_efficiency']:>5.1f}%)")
    
    print("\n" + "=" * 80)
    print("💡 性能优化建议:")
    print("=" * 80)
    print("1. 对于少量股票（<100只），串行模式可能更简单")
    print("2. 对于大量股票（>500只），多进程模式显著提升效率")
    print("3. 进程数建议设置为CPU核心数，过多进程可能导致资源竞争")
    print("4. 考虑网络带宽和API限制，避免过度并发")
    print("5. 监控系统资源使用情况，适当调整进程数")
    
    print(f"\n🔧 当前配置:")
    print(f"   推荐进程数: {config.get_process_count()}")
    print(f"   最大进程数: {config.multiprocess_config['max_processes']}")
    print(f"   启动方法: {config.multiprocess_config['start_method']}")


if __name__ == "__main__":
    main()
