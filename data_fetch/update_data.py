import baostock as bs

from datetime import datetime
from stock.stock_data import StockData
from stock.stock_daily import StockDaily
from stock.stock_daily_adj import StockDailyAdj
from stock.stock_5min import Stock5Min
from stock.stock_15min import Stock15Min
from stock.stock_30min import Stock30Min
from stock.stock_60min import Stock60Min
from index.index_daily import IndexDaily
from market.stock_industry import StockIndustry


def update_for_stock(stock_code: str):
    # 更新单只股票的各种数据
    StockDaily(data_dir=StockData().data_dir, stock_code=stock_code).update()
    StockDailyAdj(data_dir=StockData().data_dir, stock_code=stock_code).update()
    Stock5Min(data_dir=StockData().data_dir, stock_code=stock_code).update()
    Stock15Min(data_dir=StockData().data_dir, stock_code=stock_code).update()
    Stock30Min(data_dir=StockData().data_dir, stock_code=stock_code).update()
    Stock60Min(data_dir=StockData().data_dir, stock_code=stock_code).update()


if __name__ == "__main__":
    # 设置数据存储目录
    StockData().setup(data_dir="/home/<USER>/Studios/600-codes/dataset/trade_data")
    # StockData().setup(data_dir="/home/<USER>/Studios/700-dataset/stocks")
    # StockData().setup(data_dir="E:\600-codes\009-dataset")
    # 读取当前日期
    today = datetime.now().strftime("%Y-%m-%d")
    bs.login()
    # 获取证券市场基本数据，每周更新一次
    # 如果使用tushare，需要2000积分
    # 暂时使用baostock，可以获取股票/指数/其他/可转债/ETF/基金的证券代码、证券名词、上市日期、退市日期以及上市状态
    StockData().stock_basic.update()  # 股票基本信息，保存路径：data_dir/security_basic/stock_basic.csv
    StockData().index_basic.update()  # 指数基本信息，保存路径：data_dir/security_basic/index_basic.csv
    StockData().cb_basic.update()  # 可转债基本信息，保存路径：data_dir/security_basic/cb_basic.csv
    StockData().etf_basic.update()  # ETF基本信息，保存路径：data_dir/security_basic/etf_basic.csv
    # 更新上市公司信息，每周更新一次
    # 使用tushare，需要120积分，每2秒钟执行1次
    StockData().stock_company.update()  # 上市公司信息，保存路径：data_dir/security_basic/stock_company.csv
    # 更新hs300信息
    StockData().hs300.update()
    # 更新zz500信息
    StockData().zz500.update()

    # # 更新市场相关数据
    # # 龙虎榜每日明细：tushare下载，需要2000积分
    # # 龙虎榜机构交易明细：tushare下载，需要2000积分
    # # 大宗交易：tushare下载，需要300积分
    # # 融资融券每日交易汇总：tushare下载，需要2000积分
    # # 融资融券交易明细：tushare下载，需要2000积分
    # # 行业分类数据：baostock获取，每周更新一次
    # StockIndustry(data_dir=StockData().data_dir).update()
    # 指数数据，tushare需要2000积分，baostock的基本指数数据在stock_basic中已经下载
    # IndexBasic(data_dir=StockData().data_dir).update()

    # 更新指数日线数据，在default_index_pool中维护一个指数池
    index_list = StockData().index_basic.index_codes()
    for index_code in index_list:
        IndexDaily(data_dir=StockData().data_dir, index_code=index_code).update()

    # 更新股票5、15、30、60分钟及日线数据
    stock_list = StockData().stock_basic.stock_codes()
    for stock_code in stock_list:
        update_for_stock(stock_code)  # stock_code格式: sh.XXXXXX

    bs.logout()
