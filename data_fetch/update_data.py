import baostock as bs
import multiprocessing as mp
import logging
import time
from typing import List, Tuple
from datetime import datetime
from stock.stock_data import StockData
from stock.stock_daily import StockDaily
from stock.stock_daily_adj import StockDailyAdj
from stock.stock_5min import Stock5Min
from stock.stock_15min import Stock15Min
from stock.stock_30min import Stock30Min
from stock.stock_60min import Stock60Min
from index.index_daily import IndexDaily
from update_data_config import UpdateConfig


def update_for_stock(stock_code: str, data_dir: str):
    """
    更新单只股票的各种数据
    注意：每个进程需要独立的baostock连接
    """
    try:
        # 每个进程需要独立登录baostock
        login_result = bs.login()
        if login_result.error_code != "0":
            logging.error(f"进程中baostock登录失败: {login_result.error_msg}")
            return False, stock_code, f"登录失败: {login_result.error_msg}"

        # 更新各种数据
        StockDaily(data_dir=data_dir, stock_code=stock_code).update()
        StockDailyAdj(data_dir=data_dir, stock_code=stock_code).update()
        Stock5Min(data_dir=data_dir, stock_code=stock_code).update()
        Stock15Min(data_dir=data_dir, stock_code=stock_code).update()
        Stock30Min(data_dir=data_dir, stock_code=stock_code).update()
        Stock60Min(data_dir=data_dir, stock_code=stock_code).update()

        # 登出baostock
        bs.logout()

        return True, stock_code, "更新成功"

    except Exception as e:
        # 确保在异常情况下也要登出
        try:
            bs.logout()
        except:
            pass
        error_msg = f"更新失败: {str(e)}"
        logging.error(f"{stock_code} {error_msg}")
        return False, stock_code, error_msg


def update_stocks_batch(
    stock_codes: List[str], data_dir: str, process_id: int
) -> List[Tuple[bool, str, str]]:
    """
    批量更新股票数据（单个进程处理）
    """
    results = []
    total_stocks = len(stock_codes)

    logging.info(f"进程 {process_id} 开始处理 {total_stocks} 只股票")

    for i, stock_code in enumerate(stock_codes, 1):
        start_time = time.time()
        success, code, message = update_for_stock(stock_code, data_dir)
        elapsed_time = time.time() - start_time

        results.append((success, code, message))

        if success:
            logging.info(
                f"进程 {process_id} [{i}/{total_stocks}] {stock_code} 更新成功 (耗时: {elapsed_time:.2f}s)"
            )
        else:
            logging.error(
                f"进程 {process_id} [{i}/{total_stocks}] {stock_code} 更新失败: {message}"
            )

    logging.info(
        f"进程 {process_id} 完成，成功: {sum(1 for r in results if r[0])}/{total_stocks}"
    )
    return results


def split_list(lst: List, n: int) -> List[List]:
    """
    将列表分割成n个子列表
    """
    k, m = divmod(len(lst), n)
    return [lst[i * k + min(i, m) : (i + 1) * k + min(i + 1, m)] for i in range(n)]


def update_index_data(data_dir: str):
    """
    更新指数数据（单进程处理，因为指数数量相对较少）
    """
    logging.info("开始更新指数数据...")

    # 需要重新设置StockData，因为这可能在子进程中运行
    StockData().setup(data_dir=data_dir)

    # 登录baostock
    login_result = bs.login()
    if login_result.error_code != "0":
        logging.error(f"指数数据更新时baostock登录失败: {login_result.error_msg}")
        return

    try:
        # 更新指数日线数据
        index_list = StockData().index_basic.index_codes()
        total_indices = len(index_list)

        for i, index_code in enumerate(index_list, 1):
            try:
                IndexDaily(data_dir=data_dir, index_code=index_code).update()
                logging.info(f"指数数据更新 [{i}/{total_indices}] {index_code} 完成")
            except Exception as e:
                logging.error(f"指数 {index_code} 更新失败: {str(e)}")

        logging.info("指数数据更新完成")

    finally:
        bs.logout()


def main():
    """
    主函数：多进程更新股票数据
    """
    config = UpdateConfig()

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format=config.logging_config["format"],
        handlers=[
            logging.FileHandler(config.logging_config["file"]),
            logging.StreamHandler(),
        ],
    )

    # 设置数据存储目录（使用配置文件）
    data_dir = config.get_data_dir()
    StockData().setup(data_dir=data_dir)

    logging.info(f"使用数据目录: {data_dir}")

    # 读取当前日期
    today = datetime.now().strftime("%Y-%m-%d")
    logging.info(f"开始数据更新任务，日期: {today}")

    # 主进程登录baostock进行基础数据更新
    login_result = bs.login()
    if login_result.error_code != "0":
        logging.error(f"主进程baostock登录失败: {login_result.error_msg}")
        return

    try:
        # 获取证券市场基本数据，每周更新一次
        logging.info("开始更新基础数据...")
        StockData().stock_basic.update()  # 股票基本信息
        StockData().index_basic.update()  # 指数基本信息
        StockData().cb_basic.update()  # 可转债基本信息
        StockData().etf_basic.update()  # ETF基本信息
        StockData().stock_company.update()  # 上市公司信息
        StockData().hs300.update()  # hs300信息
        StockData().zz500.update()  # zz500信息
        logging.info("基础数据更新完成")

    finally:
        bs.logout()

    # 获取股票和指数列表
    stock_list = StockData().stock_basic.stock_codes()
    total_stocks = len(stock_list)
    logging.info(f"共需要更新 {total_stocks} 只股票的数据")

    # 确定进程数量（使用配置文件）
    num_processes = config.get_process_count()
    logging.info(f"使用 {num_processes} 个进程进行并行更新")

    # 分割股票列表
    stock_batches = split_list(stock_list, num_processes)

    # 记录开始时间
    start_time = time.time()

    # 使用进程池进行并行处理
    with mp.Pool(processes=num_processes) as pool:
        # 准备参数
        args = [(batch, data_dir, i) for i, batch in enumerate(stock_batches)]

        # 并行执行股票数据更新
        logging.info("开始并行更新股票数据...")
        results = pool.starmap(update_stocks_batch, args)

    # 统计结果
    total_success = 0
    total_failed = 0
    failed_stocks = []

    for batch_results in results:
        for success, stock_code, message in batch_results:
            if success:
                total_success += 1
            else:
                total_failed += 1
                failed_stocks.append((stock_code, message))

    # 更新指数数据（单进程）
    logging.info("开始更新指数数据...")
    update_index_data(data_dir)

    # 计算总耗时
    total_time = time.time() - start_time

    # 输出统计信息
    logging.info("=" * 60)
    logging.info("数据更新完成！")
    logging.info(f"总耗时: {total_time:.2f} 秒")
    logging.info(f"股票总数: {total_stocks}")
    logging.info(f"更新成功: {total_success}")
    logging.info(f"更新失败: {total_failed}")

    if failed_stocks:
        logging.warning("失败的股票:")
        for stock_code, error_msg in failed_stocks[:10]:  # 只显示前10个失败的
            logging.warning(f"  {stock_code}: {error_msg}")
        if len(failed_stocks) > 10:
            logging.warning(f"  ... 还有 {len(failed_stocks) - 10} 个失败")

    logging.info("=" * 60)


if __name__ == "__main__":
    # 设置多进程启动方法（在某些系统上需要）
    mp.set_start_method("spawn", force=True)
    main()
