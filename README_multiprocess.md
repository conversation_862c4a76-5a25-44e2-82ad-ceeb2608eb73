# 多进程数据更新说明

## 概述

由于 baostock 库不支持多线程，为了提高数据更新速度，我们将原有的串行更新模式改造为多进程并行更新模式。

## 🚀 快速开始

### 1. 激活虚拟环境
```bash
source .venv/bin/activate
```

### 2. 运行测试（推荐首次使用）
```bash
python run_update.py --test
```

### 3. 运行完整更新
```bash
python run_update.py --full
```

### 4. 查看性能对比
```bash
python run_update.py --performance
```

## 文件说明

### 1. `update_data.py` - 主要更新脚本（多进程版本）
- 完整的多进程数据更新实现
- 支持股票日线、分钟线数据的并行更新
- 包含错误处理、日志记录和性能统计

### 2. `update_data_test.py` - 测试版本
- 用于测试多进程功能
- 只处理少量股票（前20只）
- 只更新日线数据，用于验证多进程机制

### 3. `update_data_config.py` - 配置文件
- 集中管理多进程参数
- 可配置进程数量、数据目录、日志等

## 主要改进

### 1. 多进程架构
- **进程池管理**：使用 `multiprocessing.Pool` 管理进程
- **任务分配**：将股票列表均匀分配给各个进程
- **独立连接**：每个进程独立登录/登出 baostock

### 2. 性能优化
- **并行处理**：多个股票同时更新
- **智能分配**：根据 CPU 核心数自动调整进程数
- **批量处理**：每个进程处理一批股票

### 3. 错误处理
- **进程级错误处理**：每个进程独立处理错误
- **连接管理**：确保 baostock 连接正确释放
- **失败统计**：记录和报告失败的股票

### 4. 监控和日志
- **进度监控**：实时显示更新进度
- **性能统计**：记录耗时和成功率
- **详细日志**：包含进程ID的日志记录

## 使用方法

### 1. 测试多进程功能
```bash
# 激活虚拟环境
source .venv/bin/activate

# 运行测试版本（推荐先运行）
python update_data_test.py
```

### 2. 完整数据更新
```bash
# 激活虚拟环境
source .venv/bin/activate

# 运行完整更新
python update_data.py
```

### 3. 配置调整
编辑 `update_data_config.py` 文件：
```python
# 调整最大进程数
'max_processes': 8,

# 调整数据目录
'default': "/your/data/directory",

# 调整日志级别
'level': 'INFO',
```

## 性能对比

### 串行模式（原版本）
- 处理时间：约 N × T 秒（N=股票数量，T=单只股票处理时间）
- 资源利用：单核CPU，低并发

### 多进程模式（新版本）
- 处理时间：约 N × T / P 秒（P=进程数量）
- 资源利用：多核CPU，高并发
- 理论加速比：P倍（实际会略低）

## 注意事项

### 1. 系统要求
- Python 3.7+
- 足够的内存（每个进程需要独立内存）
- 稳定的网络连接

### 2. baostock 限制
- 每个进程需要独立的 baostock 连接
- 避免过多并发连接（建议不超过8个进程）
- 注意 API 调用频率限制

### 3. 数据一致性
- 基础数据（股票列表等）在主进程中更新
- 股票数据在子进程中并行更新
- 指数数据在单独进程中更新

### 4. 错误处理
- 单只股票失败不影响其他股票
- 进程崩溃会自动重启
- 详细的错误日志便于排查问题

## 故障排除

### 1. 进程启动失败
```bash
# 检查是否正确设置了启动方法
mp.set_start_method('spawn', force=True)
```

### 2. baostock 连接问题
- 检查网络连接
- 确认 baostock 服务状态
- 减少并发进程数量

### 3. 内存不足
- 减少进程数量
- 增加系统内存
- 分批处理股票

### 4. 权限问题
- 确认数据目录写权限
- 检查日志文件权限

## 监控和维护

### 1. 日志文件
- `update_data.log` - 完整更新日志
- `update_data_test.log` - 测试日志

### 2. 性能监控
```bash
# 监控进程状态
ps aux | grep python

# 监控系统资源
htop
```

### 3. 定期维护
- 清理旧日志文件
- 检查数据完整性
- 更新配置参数

## 扩展功能

### 1. 自定义更新策略
- 可选择更新特定类型的数据
- 支持增量更新模式
- 可配置重试机制

### 2. 分布式扩展
- 可扩展为多机器分布式处理
- 支持任务队列管理
- 可集成消息队列系统

### 3. 监控集成
- 可集成 Prometheus 监控
- 支持告警通知
- 可视化性能仪表板
