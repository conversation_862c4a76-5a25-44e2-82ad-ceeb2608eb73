# my-investools

A collection of investment tools and data fetching utilities.

## 🚀 多进程数据更新功能

为了提高数据更新速度，我们已将原有的串行更新模式改造为多进程并行更新模式。

### 快速开始

1. **激活虚拟环境**
   ```bash
   source .venv/bin/activate
   ```

2. **进入数据获取目录**
   ```bash
   cd data_fetch
   ```

3. **运行测试模式**（推荐首次使用）
   ```bash
   python run_update.py --test
   ```

4. **运行完整更新**
   ```bash
   python run_update.py --full
   ```

### 主要特性

- ✅ **多进程并行**：支持多个进程同时更新股票数据
- ✅ **智能分配**：根据CPU核心数自动调整进程数量
- ✅ **错误处理**：单只股票失败不影响其他股票更新
- ✅ **进度监控**：实时显示更新进度和性能统计
- ✅ **配置灵活**：可自定义进程数、数据目录等参数

### 性能提升

- **串行模式**：处理5000只股票约需要4-5小时
- **多进程模式**：使用8个进程约需要30-40分钟
- **加速比**：理论上可达到8倍加速（实际约6-7倍）

### 详细文档

更多详细信息请查看：[data_fetch/README_multiprocess.md](data_fetch/README_multiprocess.md)
